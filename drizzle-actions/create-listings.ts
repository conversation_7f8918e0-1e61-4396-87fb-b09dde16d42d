"use server";

import { db } from "../db";
import { eq, desc, inArray } from "drizzle-orm";
import {
  vehicles,
  vehicleModel,
  vehicleMake,
  vehicleMedia,
} from "../drizzle/schema";
import { getUserAttributes } from "@/lib/serverUserAttributes";

// Get user's vehicles for listing selection
export async function getUserVehiclesForListing() {
  const userAttributes = await getUserAttributes();

  if (!userAttributes) {
    throw new Error("User not authenticated");
  }

  const partyId = userAttributes["custom:db_id"];

  if (!partyId) {
    throw new Error("User party ID not found");
  }

  // Get vehicles with media in a single query using LEFT JOIN
  const results = await db
    .select({
      // Vehicle fields
      vehicleId: vehicles.id,
      partyId: vehicles.partyId,
      modelId: vehicles.modelId,
      vinNumber: vehicles.vinNumber,
      vehicleRegistration: vehicles.vehicleRegistration,
      manufacturingYear: vehicles.manufacturingYear,
      color: vehicles.color,
      isActive: vehicles.isActive,
      vehicleCreatedAt: vehicles.createdAt,
      // Model details
      modelId_: vehicleModel.id,
      modelName: vehicleModel.model,
      modelMakeId: vehicleModel.makeId,
      makeId: vehicleMake.id,
      makeName: vehicleMake.name,
      // Media fields (will be null if no media)
      mediaId: vehicleMedia.id,
      mediaPath: vehicleMedia.mediaPath,
      mediaCreatedAt: vehicleMedia.createdAt,
    })
    .from(vehicles)
    .innerJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
    .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
    .leftJoin(vehicleMedia, eq(vehicles.id, vehicleMedia.vehicleId))
    .where(eq(vehicles.partyId, parseInt(partyId)))
    .orderBy(desc(vehicles.createdAt));

  // Group results by vehicle and collect media
  const vehicleMap = new Map();

  results.forEach((row) => {
    const vehicleId = row.vehicleId;

    if (!vehicleMap.has(vehicleId)) {
      vehicleMap.set(vehicleId, {
        id: row.vehicleId,
        partyId: row.partyId,
        modelId: row.modelId,
        vinNumber: row.vinNumber,
        vehicleRegistration: row.vehicleRegistration,
        manufacturingYear: row.manufacturingYear,
        color: row.color,
        isActive: row.isActive,
        model: {
          id: row.modelId_,
          model: row.modelName,
          makeId: row.modelMakeId,
          make: {
            id: row.makeId,
            name: row.makeName,
          },
        },
        media: [],
      });
    }

    // Add media if it exists
    if (row.mediaId) {
      vehicleMap.get(vehicleId).media.push({
        id: row.mediaId,
        vehicle_id: vehicleId,
        media_path: row.mediaPath,
        created_at: row.mediaCreatedAt?.toString() || "",
      });
    }
  });

  return Array.from(vehicleMap.values());
}
