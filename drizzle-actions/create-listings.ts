"use server";

import { db } from "../db";
import { eq, and, desc, inArray } from "drizzle-orm";
import {
  vehicles,
  vehicleModel,
  vehicleMake,
  party,
  vehicleMedia,
} from "../drizzle/schema";
import {
  h_listings,
  h_listing_approval_status,
} from "../drizzle/h_schema/listings";
import { getUserAttributes } from "@/lib/serverUserAttributes";

// Get user's vehicles for listing selection
export async function getUserVehiclesForListing() {
  const userAttributes = await getUserAttributes();

  if (!userAttributes) {
    throw new Error("User not authenticated");
  }

  const partyId = userAttributes["custom:db_id"];

  if (!partyId) {
    throw new Error("User party ID not found");
  }

  // Get vehicles owned by the user
  const userVehicles = await db
    .select({
      id: vehicles.id,
      partyId: vehicles.partyId,
      modelId: vehicles.modelId,
      vinNumber: vehicles.vinNumber,
      vehicleRegistration: vehicles.vehicleRegistration,
      manufacturingYear: vehicles.manufacturingYear,
      color: vehicles.color,
      isActive: vehicles.isActive,
      // Model details - using aliases to avoid type issues
      modelId_: vehicleModel.id,
      modelName: vehicleModel.model,
      modelMakeId: vehicleModel.makeId,
      makeId: vehicleMake.id,
      makeName: vehicleMake.name,
    })
    .from(vehicles)
    .innerJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
    .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
    .where(eq(vehicles.partyId, parseInt(partyId)))
    .orderBy(desc(vehicles.createdAt));

  // Get media for each vehicle
  const vehicleIds: number[] = userVehicles.map((v) => v.id as number);

  const mediaResults =
    vehicleIds.length > 0
      ? await db
          .select()
          .from(vehicleMedia)
          .where(inArray(vehicleMedia.vehicleId, vehicleIds))
      : [];

  // Group media by vehicle ID
  const mediaByVehicleId = mediaResults.reduce(
    (acc, media) => {
      if (!acc[media.vehicleId]) {
        acc[media.vehicleId] = [];
      }
      acc[media.vehicleId].push({
        id: media.id,
        vehicle_id: media.vehicleId,
        media_path: media.mediaPath,
        created_at: media.createdAt?.toString() || "",
      });
      return acc;
    },
    {} as Record<number, any[]>
  );

  // Add media to each vehicle and reconstruct nested structure
  return userVehicles.map((vehicle) => ({
    id: vehicle.id,
    partyId: vehicle.partyId,
    modelId: vehicle.modelId,
    vinNumber: vehicle.vinNumber,
    vehicleRegistration: vehicle.vehicleRegistration,
    manufacturingYear: vehicle.manufacturingYear,
    color: vehicle.color,
    isActive: vehicle.isActive,
    model: {
      id: vehicle.modelId_,
      model: vehicle.modelName,
      makeId: vehicle.modelMakeId,
      make: {
        id: vehicle.makeId,
        name: vehicle.makeName,
      },
    },
    media: mediaByVehicleId[vehicle.id as number] || [],
  }));
}
